cd .\PS_version\
remove-item -Path .\csv_script_logs.csv -Force

# Function to load configuration from JSON file
function Get-Config {
    try {
        $configPath = "..\config.json"
        if (Test-Path $configPath) {
            $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
            return $configContent
        } else {
            Write-Warning "Config file not found at $configPath, using default values"
            return $null
        }
    } catch {
        Write-Warning "Error loading config file: $_"
        return $null
    }
}

# Load configuration
$config = Get-Config

function Get-BattleNetAccessToken {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ClientId,
        
        [Parameter(Mandatory=$true)]
        [string]$ClientSecret,
        
        [Parameter(Mandatory=$true)]
        [string]$Region
    )

    $authUrl        = "https://$Region.battle.net/oauth/token"
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("$ClientId`:$ClientSecret")))
    $headers        = @{Authorization = "Basic $base64AuthInfo"}
    $body           = @{grant_type = 'client_credentials'}

    try {
        $response   = Invoke-RestMethod -Uri $authUrl -Method Post -Headers $headers -Body $body -ContentType 'application/x-www-form-urlencoded'
        Write-Host "✓ Token obtained successfully" -ForegroundColor Green
        return $response.access_token
    }
    catch {
        Write-Host "✗ Error getting access token: $_" -ForegroundColor Red
        return $null
    }
}
function Get-WoWCharacter {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm          = $Realm.ToLower()
    $CharacterName  = $CharacterName.ToLower()
    
    $profileUrl     = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}
function Get-WoWCharacterEquipement {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/equipment"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}
function Get-WoWCharacterProfessions {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/professions"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}
function Get-WoWCharacterCurrency {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/achievements/statistics"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}
function Get-WoWCharacterMythicProfile {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/mythic-keystone-profile?namespace=profile-eu"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}
function Get-DungeonCompletions {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    # Get mythic dungeon data
    $mythicData = Get-WoWCharacterMythicProfile -AccessToken $AccessToken -Realm $Realm -CharacterName $CharacterName -Region $Region
    
    # Get dungeon encounters data
    $encountersData = Get-WoWCharacterEncounters -AccessToken $AccessToken -Realm $Realm -CharacterName $CharacterName -Region $Region

    # Define current season dungeons
    $seasonDungeons = @{
        'Operation: Floodgate' = 0
        'Cinderbrew Meadery' = 0
        'The Rookery' = 0
        'Darkflame Cleft' = 0
        'Priory of the Sacred Flame' = 0
        'The Motherlode' = 0
        'Operation Mechagon: Workshop' = 0
        'Theater of Pain' = 0
    }

    # Process mythic+ runs if available
    if ($mythicData.current_period.period_runs) {
        foreach ($run in $mythicData.current_period.period_runs) {
            $dungeonName = $run.dungeon.name
            if ($seasonDungeons.ContainsKey($dungeonName)) {
                $seasonDungeons[$dungeonName]++
            }
        }
    }

    # Create output object
    $dungeonStats = [PSCustomObject]@{
        CharacterName = $CharacterName
        Realm = $Realm
        Dungeons = $seasonDungeons
        TotalRuns = ($seasonDungeons.Values | Measure-Object -Sum).Sum
    }

    return $dungeonStats
}
function Get-WoWCharacterEncounters {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/encounters/dungeons?namespace=profile-eu"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}
function Get-CharacterTierToken {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Class
    )
    
    $tierTokens = @{
        'Mystic' = @('Hunter', 'Mage', 'Druid')
        'Venerated' = @('Paladin', 'Priest', 'Shaman')
        'Zenith' = @('Warrior', 'Rogue', 'Monk', 'Evoker')
        'Dreadful' = @('Death Knight', 'Warlock', 'Demon Hunter')
    }
    
    foreach ($token in $tierTokens.Keys) {
        if ($tierTokens[$token] -contains $Class) {
            return $token
        }
    }
    
    return "Unknown"
}
function Get-CharacterRole {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Class,
        
        [Parameter(Mandatory=$true)]
        [string]$Spec
    )
    
    $roleMapping = @{
        'Death Knight' = @{
            'Blood' = 'Tank'
            'Frost' = 'Melee DPS'
            'Unholy' = 'Melee DPS'
        }
        'Demon Hunter' = @{
            'Vengeance' = 'Tank'
            'Havoc' = 'Melee DPS'
        }
        'Druid' = @{
            'Guardian' = 'Tank'
            'Restoration' = 'Healer'
            'Balance' = 'Ranged DPS'
            'Feral' = 'Melee DPS'
        }
        'Evoker' = @{
            'Preservation' = 'Healer'
            'Devastation' = 'Ranged DPS'
            'Augmentation' = 'Ranged DPS'
        }
        'Hunter' = @{
            'Beast Mastery' = 'Ranged DPS'
            'Marksmanship' = 'Ranged DPS'
            'Survival' = 'Melee DPS'
        }
        'Mage' = @{
            'Arcane' = 'Ranged DPS'
            'Fire' = 'Ranged DPS'
            'Frost' = 'Ranged DPS'
        }
        'Monk' = @{
            'Brewmaster' = 'Tank'
            'Mistweaver' = 'Healer'
            'Windwalker' = 'Melee DPS'
        }
        'Paladin' = @{
            'Protection' = 'Tank'
            'Holy' = 'Healer'
            'Retribution' = 'Melee DPS'
        }
        'Priest' = @{
            'Discipline' = 'Healer'
            'Holy' = 'Healer'
            'Shadow' = 'Ranged DPS'
        }
        'Rogue' = @{
            'Assassination' = 'Melee DPS'
            'Outlaw' = 'Melee DPS'
            'Subtlety' = 'Melee DPS'
        }
        'Shaman' = @{
            'Restoration' = 'Healer'
            'Elemental' = 'Ranged DPS'
            'Enhancement' = 'Melee DPS'
        }
        'Warlock' = @{
            'Affliction' = 'Ranged DPS'
            'Demonology' = 'Ranged DPS'
            'Destruction' = 'Ranged DPS'
        }
        'Warrior' = @{
            'Protection' = 'Tank'
            'Arms' = 'Melee DPS'
            'Fury' = 'Melee DPS'
        }
    }
    
    if ($roleMapping.ContainsKey($Class) -and $roleMapping[$Class].ContainsKey($Spec)) {
        return $roleMapping[$Class][$Spec]
    }
    
    return "Unknown"
}
function Update-RaiderData {
    param (
        [Parameter(Mandatory=$true)]
        [hashtable]$RaiderData
    )

    try {
        $ConnectionString   = "Server=$MySQLServer;Database=$MySQLDatabase;Uid=$MySQLUsername;Pwd=$MySQLPassword;"
        $Connection         = New-Object MySql.Data.MySqlClient.MySqlConnection($ConnectionString)
        $Connection.Open()
        $ColumnNames = @($RaiderData.Keys)
        Write-Host "Processing data for character: $($RaiderData.CHAR_NAME) on realm: $($RaiderData.REALM)"
        Write-Host "Columns to update: $($ColumnNames -join ', ')"
        $CheckCommand = New-Object MySql.Data.MySqlClient.MySqlCommand
        $CheckCommand.Connection = $Connection
        $CheckCommand.CommandText = "SELECT COUNT(*) FROM $TableName WHERE CHAR_NAME = @CharName AND REALM = @Realm"
        $CharNameParam = New-Object MySql.Data.MySqlClient.MySqlParameter("@CharName", $RaiderData.CHAR_NAME)
        $RealmParam = New-Object MySql.Data.MySqlClient.MySqlParameter("@Realm", $RaiderData.REALM)
        $CheckCommand.Parameters.Add($CharNameParam)
        $CheckCommand.Parameters.Add($RealmParam)
        Write-Host "Executing check query: $($CheckCommand.CommandText)"
        $Exists = [int]$CheckCommand.ExecuteScalar()
        Write-Host "Record exists: $Exists"

        $Command = New-Object MySql.Data.MySqlClient.MySqlCommand
        $Command.Connection = $Connection

        if ($Exists -eq 0) {
            # INSERT 
            $Columns = $ColumnNames -join ","
            $Params = ($ColumnNames | ForEach-Object { "@$_" }) -join ","
            $Command.CommandText = "INSERT INTO $TableName ($Columns) VALUES ($Params)"
            Write-Host "Inserting new record..."
        } else {
            # UPDATE 
            $UpdateSet = ($ColumnNames | Where-Object { $_ -notin @('CHAR_NAME', 'REALM') } | 
                         ForEach-Object { "$_ = @$_" }) -join ","
            $Command.CommandText = "UPDATE $TableName SET $UpdateSet WHERE CHAR_NAME = @CHAR_NAME AND REALM = @REALM"
            Write-Host "Updating existing record..."
        }

        Write-Host "SQL Command: $($Command.CommandText)"

     
        foreach ($Column in $ColumnNames) {
            $Value = $RaiderData[$Column]
            $Param = New-Object MySql.Data.MySqlClient.MySqlParameter("@$Column", $Value)
            if ($null -eq $Value) {
                $Param.Value = [DBNull]::Value
            }
            $Command.Parameters.Add($Param)
            Write-Host "Added parameter @$Column = $Value"
        }

      
        Write-Host "Executing command..."
        $Result = $Command.ExecuteNonQuery()
        Write-Host "Operation completed successfully. Affected rows: $Result"

    } catch {
        Write-Host "An error occurred: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.InnerException) {
            Write-Host "Inner Exception: $($_.Exception.InnerException.Message)" -ForegroundColor Red
        }

        if ($Command -and $Command.LastInsertedId) {
            Write-Host "Last Inserted ID: $($Command.LastInsertedId)"
        }
        Write-Host "Full Exception Details: $($_.Exception)" -ForegroundColor Red
    } finally {
        if ($Connection -and $Connection.State -eq 'Open') {
            $Connection.Close()
            Write-Host "Connection closed."
        }
    }
}
function Write-ScriptLog {
    param (
        [string]$Event,
        [string]$Status,
        [string]$Details
    )
    
    $logEntry = [PSCustomObject]@{
        Timestamp = (Get-Date ).AddHours(2)
        Event = $Event
        Status = $Status
        Details = $Details
    }
    
    $logEntry | Export-Csv -Path ".\csv_script_logs.csv" -Append -NoTypeInformation
}

try{
    Write-ScriptLog -Event "Credentials" -Status "Info" -Details "Loading API credentials"
    $api_cred = Import-Csv -Delimiter "," -Path .\csv_api.csv
    Write-ScriptLog -Event "Credentials" -Status "Info" -Details "API credentials loaded"
    Write-ScriptLog -Event "Token Request" -Status "Info" -Details "Requesting Battle.net access token"
    $token = Get-BattleNetAccessToken -ClientId $api_cred.client -ClientSecret $api_cred.secret -Region "eu"
    Write-ScriptLog -Event "Token Request" -Status "Info" -Details "Battle.net access token received"
}
catch{
    Write-ScriptLog -Event "Credentials" -Status "Error" -Details "Failed to load API credentials"
    Write-ScriptLog -Event "Token Request" -Status "Error" -Details "Failed to access Battle.net"
}

try{
    Write-ScriptLog -Event "Raiders" -Status "Info" -Details "Loading raiders"
    $raiders = Import-Csv -Delimiter "," -Path .\csv_registers.csv | where status -eq "approved" | ForEach-Object {
        if ($_.url -match 'character/\w+/([^/]+)/([^/]+)$') {
            @{
                realm = $matches[1]
                name = $matches[2]
            }
        }
    } | Where-Object {
        $_.name -and $_.name.Trim() -and 
        $_.realm -and $_.realm.Trim()
    }
    Write-ScriptLog -Event "Raiders" -Status "Info" -Details "Raiders loaded"
}
catch{
    Write-ScriptLog -Event "Raiders" -Status "Error" -Details "Failed to load raiders"
}
    

## save a daily version
$date = Get-Date -Format "yyyyMMdd"

try{
    Write-ScriptLog -Event "Data" -Status "Info" -Details "Getting character data"
    $Array = @()
    foreach($raider in $raiders){
        $character      = Get-WoWCharacter -AccessToken $token -Realm $raider.realm -CharacterName $raider.name
        $characterEq    = Get-WoWCharacterEquipement -AccessToken $token -Realm $raider.realm -CharacterName $raider.name
        $characterCur   = Get-WoWCharacterCurrency -AccessToken $token -Realm $raider.realm -CharacterName $raider.name
        $charactermplus = Get-WoWCharacterMythicProfile -AccessToken $token -Realm $raider.realm -CharacterName $raider.name

        $realm          = $character.realm              | Select-Object name
        $race           = $character.race               | Select-Object name
        $class          = $character.character_class    | Select-Object name
        $faction        = $character.faction            | Select-Object name
        $spec           = $character.active_spec        | Select-Object name

        $tierToken      = Get-CharacterTierToken -Class $class.name
        $role           = Get-CharacterRole -Class $class.name -Spec $spec.name
        $Dung           = $characterCur.categories | Where-Object id -EQ 130
        $valor          = $dung.statistics | Where-Object id -EQ 20488 | Select-Object name, quantity
        $Wcrest         = $dung.statistics | Where-Object id -EQ 41786 | Select-Object name, quantity
        $Ccrest         = $dung.statistics | Where-Object id -EQ 41789 | Select-Object name, quantity
        $Rcrest         = $dung.statistics | Where-Object id -EQ 41790 | Select-Object name, quantity
        $Gcrest         = $dung.statistics | Where-Object id -EQ 41791 | Select-Object name, quantity

        $gear           = $characterEq.equipped_items | Select-Object slot,name,level,sockets,enchantments,spells,set,item_subclass

        $embel          = $gear | Where-Object spells -Like '*embellis*'
        $embel0         = if($null -eq $embel){0}elseif($embel.name.Count -eq 1){1}elseif($embel.name.Count -eq 2){2}
        $embel1         = $gear.spells | Where-Object { $_.spell -Like '*Dawnthread*' -or $_.spell -Like '*Blessed*' }
        $embel2         = if($null -eq $embel1){0}elseif($embel1.description.count -eq 1){1}elseif($embel1.description.count -eq 2){2}
        $set            = $gear | Where-Object set -like '*/5*'
        $helmet         = $gear | Where-Object slot -like '*Head*'
        $Neck           = $gear | Where-Object slot -like '*Neck*'
        $Shoulders      = $gear | Where-Object slot -like '*Shoulders*'
        $Chest          = $gear | Where-Object slot -like '*Chest*'
        $Waist          = $gear | Where-Object slot -like '*Waist*'
        $Legs           = $gear | Where-Object slot -like '*Legs*'
        $Feet           = $gear | Where-Object slot -like '*Feet*'
        $Wrist          = $gear | Where-Object slot -like '*Wrist*'
        $Hands          = $gear | Where-Object slot -like '*Hands*'
        $FINGER_1       = $gear | Where-Object slot -like '*FINGER_1*'
        $FINGER_2       = $gear | Where-Object slot -like '*FINGER_2*'
        $TRINKET_1      = $gear | Where-Object slot -like '*TRINKET_1*'
        $TRINKET_2      = $gear | Where-Object slot -like '*TRINKET_2*'
        $Back           = $gear | Where-Object slot -like '*Back*'
        $MAIN_HAND      = $gear | Where-Object slot -like '*MAIN_HAND*'
        $OFF_HAND       = $gear | Where-Object slot -like '*OFF_HAND*'

        $sockethelmet   = $helmet.sockets       | Select-Object item
        $socketsneck    = $neck.sockets         | Select-Object item
        $socketsshould  = $Shoulders.sockets    | Select-Object item
        $socketschest   = $chest.sockets        | Select-Object item
        $socketsWaist   = $Waist.sockets        | Select-Object item
        $socketsLegs    = $Legs.sockets         | Select-Object item
        $socketsFeet    = $Feet.sockets         | Select-Object item
        $socketsWrist   = $Wrist.sockets        | Select-Object item
        $socketsHands   = $Hands.sockets        | Select-Object item
        $socketsfin1    = $FINGER_1.sockets     | Select-Object item
        $socketsfin2    = $FINGER_2.sockets     | Select-Object item
        $socketsBack    = $Back.sockets         | Select-Object item
        $tierslots      = $set                  | Select-Object slot


        $table  = [ordered]@{}

        $table.add('Helmet_is_tier',0)
        $table.add('Shoulder_is_tier',0)
        $table.add('Chest_is_tier',0)
        $table.add('Legs_is_tier',0)
        $table.add('Hands_is_tier',0)

        foreach($tier in $tierslots){

            if($tier.slot.name -like 'Head'){$table['Helmet_is_tier']=1}
            elseif($tier.slot.name -like 'Shoulders'){$table['Shoulder_is_tier']=1}
            elseif($tier.slot.name -like 'Chest'){$table['Chest_is_tier']=1}
            elseif($tier.slot.name -like 'Legs'){$table['Legs_is_tier']=1}
            elseif($tier.slot.name -like 'Hands'){$table['Hands_is_tier']=1}
        }

        # Calculate tier pieces count (only pieces with ilvl >= configured tier level)
        $tierilvl = if ($config -and $config.game_settings.tier_ilvl) { $config.game_settings.tier_ilvl } else { 684 }
        $tierPiecesCount = 0
        if($null -ne $helmet.set -and $helmet.level.value -ge $tierilvl) { $tierPiecesCount++ }
        if($null -ne $Shoulders.set -and $Shoulders.level.value -ge $tierilvl) { $tierPiecesCount++ }
        if($null -ne $chest.set -and $chest.level.value -ge $tierilvl) { $tierPiecesCount++ }
        if($null -ne $legs.set -and $legs.level.value -ge $tierilvl) { $tierPiecesCount++ }
        if($null -ne $hands.set -and $hands.level.value -ge $tierilvl) { $tierPiecesCount++ }

        $Array += @(
            [pscustomobject]@{  
                            ## main information about character
                            Charname            =   $character.name;
                            Realm               =   $realm.name;
                            Race                =   $race.name;
                            Class               =   $class.name;
                            Spec                =   $Spec.name;
                            Role                =   $Role;
                            Tier_Token          =   $tierToken;
                            Faction             =   $Faction.name;
                            ilvl                =   $character.equipped_item_level;
                            Armor_type          =   $helmet.item_subclass.name;

                            ## Character's gear (items, ilvl, gems, enchants)
                            Helmet              =   $helmet.name +" ("+ $helmet.level.value +")";
                            Helmet_tier         =   if($null -eq $helmet.set){0}elseif($null -ne $helmet.set -and $helmet.level.value -ge $tierilvl){1}else{0};
                            helmet_socket       =   if($helmet.sockets.count -eq 0){0}elseif($helmet.sockets.count -eq 1){1};
                            helmet_socket_name  =   if($helmet.sockets.count -eq 0){'-'}elseif($helmet.sockets.count -eq 1){$sockethelmet[0].item.name};
                            helmet_enchant	    =	if($helmet.enchantments.Count -eq 0){0}else{1};


                            Neck                =   $Neck.name +" ("+ $Neck.level.value +")";
                            neck_sockets        =   if($Neck.sockets.count -eq 0){0}elseif($Neck.sockets.count -eq 1){1}elseif($Neck.sockets.count -eq 2){2};
                            neck_gem1           =   if($Neck.sockets.count -eq 0){'-'}elseif($Neck.sockets.count -eq 1){$socketsneck[0].item.name}elseif($Neck.sockets.count -eq 2){$socketsneck[0].item.name};
                            neck_gem2           =   if($Neck.sockets.count -eq 0){'-'}elseif($Neck.sockets.count -eq 1){'-'}elseif($Neck.sockets.count -eq 2){$socketsneck[1].item.name};
                            
                            Shoulder		    =	$Shoulders.name +" ("+ $Shoulders.level.value +")";
                            Shoulder_tier	    =	if($null -eq $Shoulders.set){0}elseif($null -ne $Shoulders.set -and $Shoulders.level.value -ge $tierilvl){1}else{0};
                            Shoulders_sock	    =	if($Shoulders.sockets.count -eq 0){0}elseif($Shoulders.sockets.count -eq 1){1};
                            Shoulder_gem	    =	if($Shoulders.sockets.count -eq 0){'-'}elseif($Shoulders.sockets.count -eq 1){$socketsshould[0].item.name};

                            Chest	            =	$chest.name +" ("+ $chest.level.value +")";
                            Chest_tier	        =	if($null -eq $chest.set){0}elseif($null -ne $chest.set -and $chest.level.value -ge $tierilvl){1}else{0};
                            chest_sock	        =	if($chest.sockets.count -eq 0){0}elseif($chest.sockets.count -eq 1){1};
                            chest_gem	        =	if($chest.sockets.count -eq 0){'-'}elseif($chest.sockets.count -eq 1){$socketschest[0].item.name};
                            chest_enchant	    =	if($Chest.enchantments.Count -eq 0){0}else{1};

                            Waist	            =	$Waist.name +" ("+ $Waist.level.value +")";
                            Waist_sock	        =	if($Waist.sockets.count -eq 0){0}elseif($Waist.sockets.count -eq 1){1};
                            Waist_gem	        =	if($Waist.sockets.count -eq 0){'-'}elseif($Waist.sockets.count -eq 1){$socketsWaist[0].item.name};

                            Legs	            =	$Legs.name +" ("+ $Legs.level.value +")";
                            Legs_tier	        =	if($null -eq $legs.set){0}elseif($null -ne $legs.set -and $legs.level.value -ge $tierilvl){1}else{0};
                            Legs_sock	        =	if($Legs.sockets.count -eq 0){0}elseif($Legs.sockets.count -eq 1){1};
                            Legs_gem	        =	if($Legs.sockets.count -eq 0){'-'}elseif($Legs.sockets.count -eq 1){$socketsLegs[0].item.name};
                            Legs_enchant	    =	if($Legs.enchantments.Count -eq 0){0}else{1};

                            Feet	            =	$Feet.name +" ("+ $Feet.level.value +")";
                            Feet_sock	        =	if($Feet.sockets.count -eq 0){0}elseif($Feet.sockets.count -eq 1){1};
                            Feet_gem	        =	if($Feet.sockets.count -eq 0){'-'}elseif($Feet.sockets.count -eq 1){$socketsFeet[0].item.name};
                            Feet_enchant	    =	if($Feet.enchantments.Count -eq 0){0}else{1};

                            Wrist	            =	$Wrist.name +" ("+ $Wrist.level.value +")";
                            Wrist_sock	        =	if($Wrist.sockets.count -eq 0){0}elseif($Wrist.sockets.count -eq 1){1};
                            Wrist_gem	        =	if($Wrist.sockets.count -eq 0){'-'}elseif($Wrist.sockets.count -eq 1){$socketsWrist[0].item.name};
                            Wrist_enchant	    =	if($Wrist.enchantments.Count -eq 0){0}else{1};

                            Hands	            =	$Hands.name +" ("+ $Hands.level.value +")";
                            Hands_tier	        =	if($null -eq $hands.set){0}elseif($null -ne $hands.set -and $hands.level.value -ge $tierilvl){1}else{0};
                            Hands_sock	        =	if($Hands.sockets.count -eq 0){0}elseif($Hands.sockets.count -eq 1){1};
                            Hands_gem	        =	if($Hands.sockets.count -eq 0){'-'}elseif($Hands.sockets.count -eq 1){$socketsHands[0].item.name};
                            Hands_enchant	    =	if($Hands.enchantments.Count -eq 0){0}else{1};

                            FINGER_1	        =	$FINGER_1.name +" ("+ $FINGER_1.level.value +")";
                            FINGER_1_sockets	=	if($FINGER_1.sockets.count -eq 0){0}elseif($FINGER_1.sockets.count -eq 1){1}elseif($FINGER_1.sockets.count -eq 2){2}elseif($FINGER_1.sockets.count -eq 3){3};
                            FINGER_1_gem1	    =	if($FINGER_1.sockets.count -eq 0){'-'}else{$socketsfin1[0].item.name};
                            FINGER_1_gem2	    =	if($FINGER_1.sockets.count -eq 0){'-'}else{$socketsfin1[1].item.name};
                            FINGER_1_enchant	=	if($FINGER_1.enchantments.Count -eq 0){0}else{1};

                            FINGER_2	        =	$FINGER_2.name +" ("+ $FINGER_2.level.value +")";
                            FINGER_2_sockets	=	if($FINGER_2.sockets.count -eq 0){0}elseif($FINGER_2.sockets.count -eq 1){1}elseif($FINGER_2.sockets.count -eq 2){2}elseif($FINGER_2.sockets.count -eq 3){3};
                            FINGER_2_gem1	    =	if($FINGER_2.sockets.count -eq 0){'-'}else{$socketsfin2[0].item.name};
                            FINGER_2_gem2	    =	if($FINGER_2.sockets.count -eq 0){'-'}else{$socketsfin2[1].item.name};
                            FINGER_2_enchant	=	if($FINGER_2.enchantments.Count -eq 0){0}else{1}

                            TRINKET_1	        =	$TRINKET_1.name +" ("+ $TRINKET_1.level.value +")";
                            TRINKET_2	        =	$TRINKET_2.name +" ("+ $TRINKET_2.level.value +")";

                            Back	            =	$Back.name +" ("+ $Back.level.value +")";
                            Back_sock	        =	if($Back.sockets.count -eq 0){0}elseif($Back.sockets.count -eq 1){1};
                            Back_gem	        =	if($Back.sockets.count -eq 0){'-'}elseif($Back.sockets.count -eq 1){$socketsBack[0].item.name};
                            Back_enchant	    =	if($Back.enchantments.Count -eq 0){0}else{1};

                            MAIN_HAND	        =	$MAIN_HAND.name +" ("+ $MAIN_HAND.level.value +")";
                            MAIN_HAND_ench	    =	if($MAIN_HAND.enchantments.Count -eq 0){0}else{1};

                            OFF_HAND	        =	if($null -ne $OFF_HAND){$OFF_HAND.name +" ("+ $OFF_HAND.level.value +")"}; 
                            OFF_HAND_ench	    =	if($OFF_HAND.enchantments.Count -eq 0){0}else{1};

                            embellishments	    =	$embel0 + $embel2;

                            Tier_Pieces	        =	$tierPiecesCount;

                            ## Currencies & Rating
                            Rating              =   $charactermplus.current_mythic_rating.rating;
                            Valor               =   $valor.quantity;
                            'Weathered Harbinger Crests'=$Wcrest.quantity;
                            'Carved Harbinger Crests' =$Ccrest.quantity;
                            'Runed Harbinger Crests'=$Rcrest.quantity;
                            'Gilded Harbinger Crests'=$Gcrest.quantity;

                        }
        )

        
    }
    Write-ScriptLog -Event "Data" -Status "Info" -Details "Character data loaded"
      
}
catch{
    Write-ScriptLog -Event "Data" -Status "Error" -Details "Failed to load character data"
}   


try{
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Exporting full data"
    $Array | export-csv -Path .\csv_export.csv -Delimiter "," -NoTypeInformation
    $Array | export-csv -Path .\Archive\Export\$($date)_csv_export.csv -Delimiter "," -NoTypeInformation
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Full data exported"
}
catch{
    Write-ScriptLog -Event "Export" -Status "Error" -Details "Failed to export full data"
}


try{
    Write-ScriptLog -Event "Import" -Status "Info" -Details "Importing data"
    $export = import-csv -Path .\csv_export.csv
    Write-ScriptLog -Event "Import" -Status "Info" -Details "Data imported"
}
catch{
    Write-ScriptLog -Event "Import" -Status "Error" -Details "Failed to import data"
}

try{
    Write-ScriptLog -Event "Table" -Status "Info" -Details "Creating table"
    $table = @()
    $x = 0
    foreach($row in $export){
    $x++
    $table +=[PSCustomObject]@{
    
    id = $x
    name = $row.charname
    realm = $row.realm
    role = $row.role
    class_name = $row.class
    spec = $row.spec
    armor_type = $row.armor_type
    tier_token = $row.tier_token
    ilvl = $row.ilvl
    }}
    Write-ScriptLog -Event "Table" -Status "Info" -Details "Table created"
}
catch{
    Write-ScriptLog -Event "Table" -Status "Error" -Details "Failed to create table"
}

try{
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Exporting character table"   
    $table | export-csv -Path .\characters.csv -Delimiter "," -NoTypeInformation
    $table | export-csv -Path .\Archive\Characters\$($date)_characters.csv -Delimiter "," -NoTypeInformation
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Character table exported"
}
catch{
    Write-ScriptLog -Event "Export" -Status "Error" -Details "Failed to export character table"
}

try{
    Write-ScriptLog -Event "Table" -Status "Info" -Details "Creating tier table"
    $table = @()
    $x = 0
foreach($row in $export){
    $x++
    $table +=[PSCustomObject]@{
    
    id = $x
    name = $row.charname
    realm = $row.realm
    token = $row.tier_token
    tier_Pieces = $row.Tier_Pieces
    Head = $row.Helmet_tier
    Shoulders = $row.Shoulder_tier
    Chest = $row.Chest_tier
    Legs = $row.Legs_tier
    Hands = $row.Hands_tier
       
    
}}
    Write-ScriptLog -Event "Table" -Status "Info" -Details "Tier table created"
}
catch{
    Write-ScriptLog -Event "Table" -Status "Error" -Details "Failed to create tier table"
}

try{
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Exporting tier table"
    $table | export-csv -Path .\csv_tier_pieces.csv -Delimiter "," -NoTypeInformation
    $table | export-csv -Path .\Archive\Tier\$($date)_csv_tier_pieces.csv -Delimiter "," -NoTypeInformation
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Tier table exported"
}
catch{
    Write-ScriptLog -Event "Export" -Status "Error" -Details "Failed to export tier table"
}



try{
    Write-ScriptLog -Event "Table" -Status "Info" -Details "Creating enchant table"
    $table = @()
    $x = 0
foreach($row in $export){
    $x++
    $table +=[PSCustomObject]@{
    
    id = $x
    name = $row.charname
    realm = $row.realm
    Head = $row.helmet_enchant
    Back = $row.back_enchant
    Chest = $row.chest_enchant
    wrist = $row.wrist_enchant
    Legs = $row.legs_enchant
    feet = $row.feet_enchant
    finger_1 = $row.finger_1_enchant
    finger_2 = $row.finger_2_enchant
    Main_hand = $row.main_hand_ench
       
    
}}
    Write-ScriptLog -Event "Table" -Status "Info" -Details "Enchant table created"
}
catch{
    Write-ScriptLog -Event "Table" -Status "Error" -Details "Failed to create Enchant table"
}
try{
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Exporting enchant table"
    $table | export-csv -Path .\csv_enchants.csv -Delimiter "," -NoTypeInformation
    $table | export-csv -Path .\Archive\Enchants\$($date)_csv_enchants.csv -Delimiter "," -NoTypeInformation
    Write-ScriptLog -Event "Export" -Status "Info" -Details "Enchant table exported"
}
catch{
    Write-ScriptLog -Event "Export" -Status "Error" -Details "Failed to export enchant table"
}

class WarcraftLogsAPI {
    [string]$ClientId
    [string]$ClientSecret
    [string]$AccessToken

    WarcraftLogsAPI([string]$clientId, [string]$clientSecret) {
        $this.ClientId = $clientId
        $this.ClientSecret = $clientSecret
    }

    [string] GetAccessToken() {
        try {
            $authUrl = "https://www.warcraftlogs.com/oauth/token"
            $pair = "$($this.ClientId):$($this.ClientSecret)"
            $encodedCreds = [System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes($pair))
            
            $headers = @{
                Authorization = "Basic $encodedCreds"
            }
            
            $body = @{
                grant_type = "client_credentials"
            }

            $response = Invoke-RestMethod -Uri $authUrl -Method Post -Headers $headers -Body $body
            $this.AccessToken = $response.access_token
            Write-Host "Successfully obtained access token"
            return $this.AccessToken
        }
        catch {
            Write-Error "Error getting access token: $_"
            throw
        }
    }

    [PSCustomObject] ExecuteQuery([string]$query, [PSCustomObject]$variables) {
        try {
            if (-not $this.AccessToken) {
                $this.GetAccessToken()
            }

            $headers = @{
                Authorization = "Bearer $($this.AccessToken)"
                "Content-Type" = "application/json"
            }

            $body = @{
                query = $query
                variables = $variables
            } | ConvertTo-Json

            Write-Host "Sending query with variables: $($variables | ConvertTo-Json)"
            
            $response = Invoke-RestMethod -Uri "https://www.warcraftlogs.com/api/v2/client" `
                -Method Post -Headers $headers -Body $body -ContentType "application/json"

            if ($response.errors) {
                Write-Error "GraphQL errors: $($response.errors | ConvertTo-Json -Depth 10)"
                throw "GraphQL query returned errors"
            }

            return $response
        }
        catch {
            Write-Error "Error executing query: $_"
            throw
        }
    }
}
function Get-GuildReports {
    param (
        [Parameter(Mandatory=$true)]
        [WarcraftLogsAPI]$Api,
        
        [Parameter(Mandatory=$true)]
        [int]$GuildId
    )

    $query = @"
    query GuildReports(`$guildId: Int!) {
        reportData {
            reports(guildID: `$guildId, limit:55) {
                current_page
                last_page
                total
                per_page
                data {
                    zone{
                      name
                    }
                    code
                    startTime
                    owner {
                        name
                    }
                    fights {
                        name
                        startTime
                        endTime
                        encounterID
                        difficulty
                        kill
                        id
                    }
                }
            }
        }
    }
"@

    $variables = @{
        guildId = $GuildId
    }

    try {
        $response = $Api.ExecuteQuery($query, $variables)
        $reportData = $response.data.reportData.reports.data

        if (-not $reportData) {
            Write-Host "No report data found in the response"
            return @()
        }

        $rows = @()
        $link = "https://www.warcraftlogs.com/reports/"

        foreach ($report in $reportData) {
            $owner = if ($report.owner.name) { $report.owner.name } else { "Unknown" }
            $reportDate = [DateTimeOffset]::FromUnixTimeMilliseconds($report.startTime).ToString('yyyy-MM-dd')

            foreach ($fight in $report.fights) {
                if ($fight.encounterID) {
                    $durationSeconds = [math]::Floor(($fight.endTime - $fight.startTime) / 1000)
                    $minutes = [math]::Floor($durationSeconds / 60)
                    $seconds = $durationSeconds % 60
                    $durationStr = $minutes.ToString() + ":" + $seconds.ToString("00")


                    $encounterName = switch ($fight.difficulty) {
                        3 { "$($fight.name) Normal" }
                        4 { "$($fight.name) Heroic" }
                        default { "$($fight.name) +$($fight.difficulty)" }
                    }

                    $rows += [PSCustomObject]@{
                        Date = $reportDate
                        Encounter = $encounterName
                        Duration = $durationStr
                        Kill = $fight.kill
                        Owner = $owner
                        Link = "$link$($report.code)/?fight=$($fight.id)"
                        Zone = $report.zone.name
                    }
                }
            }
        }

        Write-Host "Successfully processed $($rows.Count) fights from $($reportData.Count) reports"
        return $rows | Sort-Object Date, Encounter
    }
    catch {
        Write-Error "Error fetching reports: $_"
        Write-Error $_.ScriptStackTrace
        return @()
    }
}


# Use configuration values or fallback to defaults
$ClientId = if ($config -and $config.warcraft_logs.client_id) { $config.warcraft_logs.client_id } else { "9dc8aaec-23bc-443e-8eef-8054a905cb82" }
$ClientSecret = if ($config -and $config.warcraft_logs.client_secret) { $config.warcraft_logs.client_secret } else { "TIu343dsvPAGWa4O86S9MuLHsnASI1iRIFjU4Ki5" }
$GuildId = if ($config -and $config.warcraft_logs.guild_id) { $config.warcraft_logs.guild_id } else { 689141 }


try {
   
    $api = [WarcraftLogsAPI]::new($ClientId, $ClientSecret)
    Write-ScriptLog -Event "Logs" -Status "Info" -Details "Fetching data from WarcraftLogs API..."
    $data = Get-GuildReports -Api $api -GuildId $GuildId | export-csv -Path ".\csv_logs.csv" -Delimiter "," -NoTypeInformation
    Write-ScriptLog -Event "Logs" -Status "Info" -Details "Data fetched from WarcraftLogs API"
    
}
catch {
    Write-ScriptLog -Event "Logs" -Status "Error" -Details "Failed to fetch data from WarcraftLogs API"
    Write-ScriptLog -Event "Logs" -Status "Error" -Details $_.ScriptStackTrace
}
